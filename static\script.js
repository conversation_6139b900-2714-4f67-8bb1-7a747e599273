class ProcessManager {
    constructor() {
        this.ws = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;

        this.initializeElements();
        this.bindEvents();
        this.connectWebSocket();
        this.startResourceMonitoring();
    }

    initializeElements() {
        this.startBtn = document.getElementById('start-btn');
        this.restartBtn = document.getElementById('restart-btn');
        this.stopBtn = document.getElementById('stop-btn');
        this.clearBtn = document.getElementById('clear-btn');
        this.statusDot = document.getElementById('status-dot');
        this.statusText = document.getElementById('status-text');
        this.startupStatus = document.getElementById('startup-status');
        this.outputContent = document.getElementById('output-content');
        this.outputContainer = document.getElementById('output-container');
        this.autoScrollCheckbox = document.getElementById('auto-scroll');

        // 资源监控元素
        this.memoryUsage = document.getElementById('memory-usage');
        this.memoryProgress = document.getElementById('memory-progress');
        this.memoryDetail = document.getElementById('memory-detail');
        this.gpuMemoryUsage = document.getElementById('gpu-memory-usage');
        this.gpuMemoryProgress = document.getElementById('gpu-memory-progress');
        this.gpuMemoryDetail = document.getElementById('gpu-memory-detail');
        this.gpuUtilization = document.getElementById('gpu-utilization');
        this.gpuUtilizationProgress = document.getElementById('gpu-utilization-progress');
        this.gpuName = document.getElementById('gpu-name');
    }

    bindEvents() {
        this.startBtn.addEventListener('click', () => this.startProcess());
        this.restartBtn.addEventListener('click', () => this.restartProcess());
        this.stopBtn.addEventListener('click', () => this.stopProcess());
        this.clearBtn.addEventListener('click', () => this.clearOutput());
    }

    connectWebSocket() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/ws`;

        try {
            this.ws = new WebSocket(wsUrl);

            this.ws.onopen = () => {
                console.log('WebSocket连接已建立');
                this.isConnected = true;
                this.reconnectAttempts = 0;
                this.addOutput('=== WebSocket连接已建立 ===', 'system');
            };

            this.ws.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    this.handleWebSocketMessage(data);
                } catch (e) {
                    console.error('解析WebSocket消息失败:', e);
                }
            };

            this.ws.onclose = () => {
                console.log('WebSocket连接已关闭');
                this.isConnected = false;
                this.addOutput('=== WebSocket连接已断开 ===', 'error');
                this.attemptReconnect();
            };

            this.ws.onerror = (error) => {
                console.error('WebSocket错误:', error);
                this.addOutput('=== WebSocket连接错误 ===', 'error');
            };

        } catch (e) {
            console.error('创建WebSocket连接失败:', e);
            this.addOutput('=== 无法建立WebSocket连接 ===', 'error');
        }
    }

    attemptReconnect() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 10000);

            this.addOutput(`=== ${delay/1000}秒后尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts}) ===`, 'system');

            setTimeout(() => {
                this.connectWebSocket();
            }, delay);
        } else {
            this.addOutput('=== 重连失败，请刷新页面 ===', 'error');
        }
    }

    handleWebSocketMessage(data) {
        if (data.type === 'output') {
            this.addOutput(data.data, 'output');
        } else if (data.type === 'status') {
            this.updateStatus(data.data);
        }
    }

    async startProcess() {
        this.setButtonsLoading(true);
        try {
            const response = await fetch('/start', { method: 'POST' });
            const result = await response.json();

            if (result.status === 'success') {
                this.addOutput('=== 启动命令已发送 ===', 'system');
            } else {
                this.addOutput(`启动失败: ${result.message}`, 'error');
            }
        } catch (error) {
            this.addOutput(`启动失败: ${error.message}`, 'error');
        } finally {
            this.setButtonsLoading(false);
        }
    }

    async restartProcess() {
        this.setButtonsLoading(true);
        try {
            const response = await fetch('/restart', { method: 'POST' });
            const result = await response.json();

            if (result.status === 'success') {
                this.addOutput('=== 重启命令已发送 ===', 'system');
            } else {
                this.addOutput(`重启失败: ${result.message}`, 'error');
            }
        } catch (error) {
            this.addOutput(`重启失败: ${error.message}`, 'error');
        } finally {
            this.setButtonsLoading(false);
        }
    }

    async stopProcess() {
        this.setButtonsLoading(true);
        try {
            const response = await fetch('/stop', { method: 'POST' });
            const result = await response.json();

            if (result.status === 'success') {
                this.addOutput('=== 停止命令已发送 ===', 'system');
            } else {
                this.addOutput(`停止失败: ${result.message}`, 'error');
            }
        } catch (error) {
            this.addOutput(`停止失败: ${error.message}`, 'error');
        } finally {
            this.setButtonsLoading(false);
        }
    }

    updateStatus(status) {
        const { is_running, startup_complete, process_alive } = status;

        // 更新状态指示器
        this.statusDot.className = 'status-dot';

        if (!is_running && !process_alive) {
            this.statusDot.classList.add('stopped');
            this.statusText.textContent = '已停止';
            this.startupStatus.textContent = '等待启动...';

            this.startBtn.disabled = false;
            this.restartBtn.disabled = true;
            this.stopBtn.disabled = true;
        } else if (is_running && !startup_complete) {
            this.statusDot.classList.add('starting');
            this.statusText.textContent = '启动中';
            this.startupStatus.textContent = '正在启动，请等待...';

            this.startBtn.disabled = true;
            this.restartBtn.disabled = false;
            this.stopBtn.disabled = false;
        } else if (is_running && startup_complete) {
            this.statusDot.classList.add('complete');
            this.statusText.textContent = '运行中';
            this.startupStatus.textContent = '启动完成！服务可用';

            this.startBtn.disabled = true;
            this.restartBtn.disabled = false;
            this.stopBtn.disabled = false;
        } else if (process_alive) {
            this.statusDot.classList.add('running');
            this.statusText.textContent = '运行中';
            this.startupStatus.textContent = '进程运行中...';

            this.startBtn.disabled = true;
            this.restartBtn.disabled = false;
            this.stopBtn.disabled = false;
        }
    }

    addOutput(text, type = 'output') {
        const timestamp = new Date().toLocaleTimeString();
        let formattedText = `[${timestamp}] `;

        switch (type) {
            case 'system':
                formattedText += `🔧 ${text}`;
                break;
            case 'error':
                formattedText += `❌ ${text}`;
                break;
            case 'output':
            default:
                formattedText += text;
                break;
        }

        this.outputContent.textContent += formattedText + '\n';

        // 自动滚动到底部
        if (this.autoScrollCheckbox.checked) {
            this.outputContainer.scrollTop = this.outputContainer.scrollHeight;
        }
    }

    clearOutput() {
        this.outputContent.textContent = '';
    }

    setButtonsLoading(loading) {
        const buttons = [this.startBtn, this.restartBtn, this.stopBtn];
        buttons.forEach(btn => {
            if (loading) {
                btn.style.opacity = '0.7';
                btn.style.cursor = 'wait';
            } else {
                btn.style.opacity = '';
                btn.style.cursor = '';
            }
        });
    }

    startResourceMonitoring() {
        // 立即获取一次资源信息
        this.updateResourceInfo();

        // 每3秒更新一次资源信息
        setInterval(() => {
            this.updateResourceInfo();
        }, 3000);
    }

    async updateResourceInfo() {
        try {
            // 获取系统内存信息
            await this.updateMemoryInfo();

            // 获取GPU信息
            await this.updateGPUInfo();

        } catch (error) {
            console.error('更新资源信息失败:', error);
        }
    }

    async updateMemoryInfo() {
        try {
            // 使用psutil通过后端API获取内存信息
            const response = await fetch('/system-resources');
            if (response.ok) {
                const data = await response.json();
                if (data.status === 'success' && data.memory) {
                    const memory = data.memory;
                    const usedGB = (memory.used / (1024 ** 3)).toFixed(1);
                    const totalGB = (memory.total / (1024 ** 3)).toFixed(1);
                    const percentage = memory.percent;

                    this.memoryUsage.textContent = `${percentage}%`;
                    this.memoryProgress.style.width = `${percentage}%`;
                    this.memoryDetail.textContent = `${usedGB}GB / ${totalGB}GB`;
                }
            }
        } catch (error) {
            this.memoryUsage.textContent = '--';
            this.memoryProgress.style.width = '0%';
            this.memoryDetail.textContent = '获取失败';
        }
    }

    async updateGPUInfo() {
        try {
            const response = await fetch('/check-gpu-usage');
            if (response.ok) {
                const data = await response.json();
                if (data.status === 'success' && data.gpu_info && data.gpu_info.nvidia_smi) {
                    const gpuData = data.gpu_info.nvidia_smi;

                    if (Array.isArray(gpuData) && gpuData.length > 0) {
                        const gpu = gpuData[0]; // 使用第一个GPU

                        // GPU显存
                        const memoryUsed = parseInt(gpu.memory_used);
                        const memoryTotal = parseInt(gpu.memory_total);
                        const memoryPercent = ((memoryUsed / memoryTotal) * 100).toFixed(1);

                        this.gpuMemoryUsage.textContent = `${memoryPercent}%`;
                        this.gpuMemoryProgress.style.width = `${memoryPercent}%`;
                        this.gpuMemoryDetail.textContent = `${memoryUsed}MB / ${memoryTotal}MB`;

                        // GPU使用率
                        const utilization = parseInt(gpu.utilization);
                        this.gpuUtilization.textContent = `${utilization}%`;
                        this.gpuUtilizationProgress.style.width = `${utilization}%`;
                        this.gpuName.textContent = gpu.name;

                    } else {
                        this.setGPUUnavailable();
                    }
                } else {
                    this.setGPUUnavailable();
                }
            } else {
                this.setGPUUnavailable();
            }
        } catch (error) {
            this.setGPUUnavailable();
        }
    }

    setGPUUnavailable() {
        this.gpuMemoryUsage.textContent = '--';
        this.gpuMemoryProgress.style.width = '0%';
        this.gpuMemoryDetail.textContent = 'GPU不可用';
        this.gpuUtilization.textContent = '--';
        this.gpuUtilizationProgress.style.width = '0%';
        this.gpuName.textContent = 'GPU不可用';
    }

    formatBytes(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    new ProcessManager();
});
