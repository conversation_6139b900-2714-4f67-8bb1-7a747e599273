<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>资源监控测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .resource-item {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 5px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #ffc107, #dc3545);
            transition: width 0.3s ease;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .log {
            background: #1e1e1e;
            color: #f8f8f2;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>资源监控测试页面</h1>
        
        <div class="resource-item">
            <h3>内存使用</h3>
            <div id="memory-info">等待获取...</div>
            <div class="progress-bar">
                <div id="memory-progress" class="progress-fill" style="width: 0%"></div>
            </div>
        </div>
        
        <div class="resource-item">
            <h3>GPU显存</h3>
            <div id="gpu-memory-info">等待获取...</div>
            <div class="progress-bar">
                <div id="gpu-memory-progress" class="progress-fill" style="width: 0%"></div>
            </div>
        </div>
        
        <div class="resource-item">
            <h3>GPU使用率</h3>
            <div id="gpu-utilization-info">等待获取...</div>
            <div class="progress-bar">
                <div id="gpu-utilization-progress" class="progress-fill" style="width: 0%"></div>
            </div>
        </div>
        
        <div>
            <button onclick="testMemoryAPI()">测试内存API</button>
            <button onclick="testGPUAPI()">测试GPU API</button>
            <button onclick="startAutoUpdate()">开始自动更新</button>
            <button onclick="stopAutoUpdate()">停止自动更新</button>
            <button onclick="clearLog()">清空日志</button>
        </div>
    </div>
    
    <div class="test-container">
        <h3>调试日志</h3>
        <div id="log" class="log"></div>
    </div>

    <script>
        let updateInterval = null;
        
        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('log').textContent = '';
        }
        
        async function testMemoryAPI() {
            log('开始测试内存API...');
            try {
                const response = await fetch('/system-resources');
                log(`内存API响应状态: ${response.status}`);
                
                if (response.ok) {
                    const data = await response.json();
                    log(`内存API响应数据: ${JSON.stringify(data, null, 2)}`);
                    
                    if (data.status === 'success' && data.memory) {
                        const memory = data.memory;
                        const usedGB = (memory.used / (1024 ** 3)).toFixed(1);
                        const totalGB = (memory.total / (1024 ** 3)).toFixed(1);
                        const percentage = memory.percent;
                        
                        document.getElementById('memory-info').textContent = 
                            `${percentage}% (${usedGB}GB / ${totalGB}GB)`;
                        document.getElementById('memory-progress').style.width = `${percentage}%`;
                        
                        log(`内存更新成功: ${percentage}%`);
                    } else {
                        log('内存数据格式错误');
                    }
                } else {
                    log(`内存API请求失败: ${response.status}`);
                }
            } catch (error) {
                log(`内存API测试失败: ${error.message}`);
            }
        }
        
        async function testGPUAPI() {
            log('开始测试GPU API...');
            try {
                const response = await fetch('/check-gpu-usage');
                log(`GPU API响应状态: ${response.status}`);
                
                if (response.ok) {
                    const data = await response.json();
                    log(`GPU API响应数据: ${JSON.stringify(data, null, 2)}`);
                    
                    if (data.status === 'success' && data.gpu_info && data.gpu_info.nvidia_smi) {
                        const gpuData = data.gpu_info.nvidia_smi;
                        
                        if (Array.isArray(gpuData) && gpuData.length > 0) {
                            const gpu = gpuData[0];
                            
                            // GPU显存
                            const memoryUsed = parseInt(gpu.memory_used);
                            const memoryTotal = parseInt(gpu.memory_total);
                            const memoryPercent = ((memoryUsed / memoryTotal) * 100).toFixed(1);
                            
                            document.getElementById('gpu-memory-info').textContent = 
                                `${memoryPercent}% (${memoryUsed}MB / ${memoryTotal}MB)`;
                            document.getElementById('gpu-memory-progress').style.width = `${memoryPercent}%`;
                            
                            // GPU使用率
                            const utilization = parseInt(gpu.utilization);
                            document.getElementById('gpu-utilization-info').textContent = 
                                `${utilization}% (${gpu.name})`;
                            document.getElementById('gpu-utilization-progress').style.width = `${utilization}%`;
                            
                            log(`GPU更新成功: 显存${memoryPercent}%, 使用率${utilization}%`);
                        } else {
                            log('GPU数据为空');
                            setGPUUnavailable();
                        }
                    } else {
                        log('GPU API返回失败或数据格式错误');
                        setGPUUnavailable();
                    }
                } else {
                    log(`GPU API请求失败: ${response.status}`);
                    setGPUUnavailable();
                }
            } catch (error) {
                log(`GPU API测试失败: ${error.message}`);
                setGPUUnavailable();
            }
        }
        
        function setGPUUnavailable() {
            document.getElementById('gpu-memory-info').textContent = 'GPU不可用';
            document.getElementById('gpu-memory-progress').style.width = '0%';
            document.getElementById('gpu-utilization-info').textContent = 'GPU不可用';
            document.getElementById('gpu-utilization-progress').style.width = '0%';
        }
        
        function startAutoUpdate() {
            if (updateInterval) {
                clearInterval(updateInterval);
            }
            
            log('开始自动更新...');
            updateInterval = setInterval(() => {
                testMemoryAPI();
                testGPUAPI();
            }, 3000);
            
            // 立即执行一次
            testMemoryAPI();
            testGPUAPI();
        }
        
        function stopAutoUpdate() {
            if (updateInterval) {
                clearInterval(updateInterval);
                updateInterval = null;
                log('停止自动更新');
            }
        }
        
        // 页面加载时自动开始测试
        window.addEventListener('load', () => {
            log('页面加载完成，开始测试...');
            testMemoryAPI();
            testGPUAPI();
        });
    </script>
</body>
</html>
