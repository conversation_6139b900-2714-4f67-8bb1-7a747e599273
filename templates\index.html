<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CF进程管理</title>
    <link rel="stylesheet" href="/static/style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>CF进程管理</h1>
        </header>

        <div class="control-panel">
            <div class="status-section">
                <div class="status-indicator">
                    <span id="status-dot" class="status-dot stopped"></span>
                    <span id="status-text">已停止</span>
                </div>
                <div class="startup-status">
                    <span id="startup-status">等待启动...</span>
                </div>
            </div>

            <div class="button-section">
                <button id="start-btn" class="btn btn-start">启动</button>
                <button id="restart-btn" class="btn btn-restart" disabled>重启</button>
                <button id="stop-btn" class="btn btn-stop" disabled>停止</button>
            </div>
        </div>

        <div class="resource-monitor">
            <h3>系统资源监控</h3>
            <div class="resource-grid">
                <div class="resource-card">
                    <div class="resource-header">
                        <span class="resource-icon">💾</span>
                        <span class="resource-title">内存使用</span>
                    </div>
                    <div class="resource-content">
                        <div class="resource-value" id="memory-usage">--</div>
                        <div class="resource-bar">
                            <div class="resource-progress" id="memory-progress"></div>
                        </div>
                        <div class="resource-detail" id="memory-detail">-- / --</div>
                    </div>
                </div>

                <div class="resource-card">
                    <div class="resource-header">
                        <span class="resource-icon">🎮</span>
                        <span class="resource-title">GPU显存</span>
                    </div>
                    <div class="resource-content">
                        <div class="resource-value" id="gpu-memory-usage">--</div>
                        <div class="resource-bar">
                            <div class="resource-progress" id="gpu-memory-progress"></div>
                        </div>
                        <div class="resource-detail" id="gpu-memory-detail">-- / --</div>
                    </div>
                </div>

                <div class="resource-card">
                    <div class="resource-header">
                        <span class="resource-icon">⚡</span>
                        <span class="resource-title">GPU使用率</span>
                    </div>
                    <div class="resource-content">
                        <div class="resource-value" id="gpu-utilization">--</div>
                        <div class="resource-bar">
                            <div class="resource-progress" id="gpu-utilization-progress"></div>
                        </div>
                        <div class="resource-detail" id="gpu-name">--</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="output-section">
            <h3>实时输出日志</h3>
            <div id="output-container" class="output-container">
                <div id="output-content"></div>
            </div>
            <div class="output-controls">
                <button id="clear-btn" class="btn btn-secondary">清空日志</button>
                <label>
                    <input type="checkbox" id="auto-scroll" checked>
                    自动滚动
                </label>
            </div>
        </div>

        <div class="info-section">
            <h3>说明</h3>
            <ul>
                <li>点击"启动"按钮启动ComfyUI</li>
                <li>当看到 <code>http://0.0.0.0:8188</code> 时表示启动完成</li>
                <li>可以随时重启或停止进程</li>
                <li>实时监控系统内存、GPU显存和GPU使用率</li>
                <li>实时日志会显示进程的所有输出</li>
            </ul>
        </div>
    </div>

    <script src="/static/script.js"></script>
</body>
</html>
