# AI视频编辑器 - 进程管理器

一个基于FastAPI和WebSocket的进程管理Web界面，用于启动、重启和监控AI视频编辑器。

## 功能特性

- 🚀 **一键启动/重启/停止** - 通过Web界面控制进程
- 📊 **实时状态监控** - 显示进程运行状态和启动进度
- 📝 **实时日志输出** - WebSocket实时显示进程输出
- 🎯 **启动检测** - 自动检测 `http://0.0.0.0:8188` 启动完成标志
- 📱 **响应式设计** - 支持桌面和移动设备
- 🔄 **自动重连** - WebSocket断线自动重连

## 安装和运行

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 启动服务

#### 方法一：使用启动脚本（推荐）
```bash
python run.py
```

#### 方法二：直接使用uvicorn
```bash
uvicorn app:app --host 0.0.0.0 --port 8000 --reload
```

### 3. 访问Web界面

打开浏览器访问：http://localhost:8000

## 使用说明

### 界面说明

1. **状态指示器**
   - 🔴 红色：进程已停止
   - 🟡 黄色：进程启动中
   - 🟢 绿色：进程运行中
   - 🔵 蓝色：启动完成（检测到 http://0.0.0.0:8188）

2. **控制按钮**
   - **启动**：启动AI视频编辑器进程
   - **重启**：停止当前进程并重新启动
   - **停止**：停止当前运行的进程

3. **实时日志**
   - 显示进程的所有输出
   - 支持自动滚动
   - 可以清空日志历史

### 启动流程

1. 点击"启动"按钮
2. 系统切换到目录：`/workspace/cpfs-data/ComfyUI`
3. 执行命令：`hai run python3.12 main.py --listen`
4. 实时显示进程输出
5. 当检测到输出包含 `http://0.0.0.0:8188` 时，状态变为"启动完成"

## 技术架构

- **后端**：FastAPI + subprocess
- **前端**：原生JavaScript + WebSocket
- **样式**：CSS3 + 响应式设计
- **实时通信**：WebSocket
- **进程管理**：Python subprocess模块

## 文件结构

```
AI视频编辑器/
├── app.py              # FastAPI主应用
├── run.py              # 启动脚本
├── requirements.txt    # Python依赖
├── README.md          # 说明文档
├── templates/
│   └── index.html     # 主页面模板
└── static/
    ├── style.css      # 样式文件
    └── script.js      # JavaScript逻辑
```

## API接口

- `GET /` - 主页面
- `POST /start` - 启动进程
- `POST /restart` - 重启进程
- `POST /stop` - 停止进程
- `GET /status` - 获取进程状态
- `WebSocket /ws` - 实时日志和状态更新

## 注意事项

1. 确保系统中已安装 `hai` 命令
2. 确保 `/workspace/cpfs-data/ComfyUI` 目录存在
3. 确保 `main.py` 文件存在于 `/workspace/cpfs-data/ComfyUI` 目录中
4. 进程启动可能需要一些时间，请耐心等待
5. 如果WebSocket连接断开，页面会自动尝试重连

## 故障排除

### 常见问题

1. **启动失败**
   - 检查 `hai` 命令是否可用
   - 检查 `/workspace/cpfs-data/ComfyUI` 目录是否存在
   - 检查 `main.py` 文件是否存在于ComfyUI目录中
   - 查看错误日志获取详细信息

2. **WebSocket连接失败**
   - 检查防火墙设置
   - 确保端口8000未被占用
   - 刷新页面重新连接

3. **页面无法访问**
   - 检查服务是否正常启动
   - 确认端口8000未被其他程序占用
   - 检查网络连接

## 开发和自定义

如需修改启动命令，请编辑 `app.py` 文件中的 `start_process` 方法：

```python
self.process = subprocess.Popen(
    ["你的命令", "参数1", "参数2"],  # 修改这里
    stdout=subprocess.PIPE,
    stderr=subprocess.STDOUT,
    universal_newlines=True,
    bufsize=1
)
```

如需修改启动完成检测字符串，请修改 `_monitor_output` 方法中的检测逻辑：

```python
if "你的启动完成标志" in line:  # 修改这里
    self.startup_complete = True
```
