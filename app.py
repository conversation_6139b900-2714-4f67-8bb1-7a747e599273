from fastapi import FastAP<PERSON>, WebSocket, Request
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
import subprocess
import asyncio
import threading
import queue
import json
from typing import Optional
import os
import signal
import psutil
import time

app = FastAPI()

# 挂载静态文件和模板
app.mount("/static", StaticFiles(directory="static"), name="static")
templates = Jinja2Templates(directory="templates")

# 全局变量
process: Optional[subprocess.Popen] = None
output_queue = queue.Queue()
is_running = False
startup_complete = False

class ProcessManager:
    def __init__(self):
        self.process = None
        self.output_queue = queue.Queue()
        self.is_running = False
        self.startup_complete = False

    def start_process(self):
        """启动进程"""
        if self.process is not None and self.process.poll() is None:
            return {"status": "error", "message": "进程已在运行"}

        try:
            # 设置工作目录为ComfyUI路径
            comfyui_path = "/workspace/cpfs-data/ComfyUI"

            self.process = subprocess.Popen(
                ["hai", "run", "python3.12", "main.py", "--listen"],
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1,
                cwd=comfyui_path,  # 设置工作目录
                preexec_fn=os.setsid if os.name != 'nt' else None  # 创建新的进程组
            )
            self.is_running = True
            self.startup_complete = False

            # 启动输出监控线程
            threading.Thread(target=self._monitor_output, daemon=True).start()

            return {"status": "success", "message": "进程启动中..."}
        except Exception as e:
            return {"status": "error", "message": f"启动失败: {str(e)}"}

    def restart_process(self):
        """重启进程"""
        self.stop_process()
        # 等待进程完全停止
        if self.process:
            self.process.wait()
        return self.start_process()

    def stop_process(self):
        """停止进程"""
        if self.process is not None and self.process.poll() is None:
            try:
                pid = self.process.pid
                print(f"正在停止进程 PID: {pid}")

                # 方法1: 尝试优雅停止
                self.process.terminate()
                try:
                    self.process.wait(timeout=3)
                    print("进程已优雅停止")
                except subprocess.TimeoutExpired:
                    print("优雅停止超时，强制杀死进程")

                    # 方法2: 强制杀死主进程
                    try:
                        self.process.kill()
                        self.process.wait(timeout=2)
                    except subprocess.TimeoutExpired:
                        pass

                    # 方法3: 使用psutil杀死进程组
                    try:
                        parent = psutil.Process(pid)
                        children = parent.children(recursive=True)

                        # 杀死所有子进程
                        for child in children:
                            try:
                                print(f"杀死子进程 PID: {child.pid}")
                                child.kill()
                            except (psutil.NoSuchProcess, psutil.AccessDenied):
                                pass

                        # 杀死主进程
                        try:
                            parent.kill()
                            print(f"杀死主进程 PID: {pid}")
                        except (psutil.NoSuchProcess, psutil.AccessDenied):
                            pass

                        # 等待进程真正结束
                        time.sleep(1)

                    except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
                        print(f"使用psutil停止进程时出错: {e}")

                    # 方法4: 使用系统命令强制杀死
                    try:
                        os.system(f"pkill -f 'hai run python3.12 main.py'")
                        os.system(f"pkill -f 'python3.12 main.py --listen'")
                        print("已执行系统级进程清理")
                    except Exception as e:
                        print(f"系统级清理出错: {e}")

            except Exception as e:
                print(f"停止进程时出错: {e}")

        self.is_running = False
        self.startup_complete = False
        self.process = None
        print("进程管理器状态已重置")

    def get_process_info(self):
        """获取进程信息用于调试"""
        if self.process is None:
            return "进程对象为空"

        try:
            pid = self.process.pid
            poll_result = self.process.poll()

            # 使用psutil获取更详细的进程信息
            try:
                proc = psutil.Process(pid)
                children = proc.children(recursive=True)
                return {
                    "pid": pid,
                    "poll_result": poll_result,
                    "psutil_status": proc.status() if proc.is_running() else "not_running",
                    "children_count": len(children),
                    "children_pids": [child.pid for child in children]
                }
            except psutil.NoSuchProcess:
                return {
                    "pid": pid,
                    "poll_result": poll_result,
                    "psutil_status": "no_such_process"
                }
        except Exception as e:
            return f"获取进程信息出错: {e}"

    def _monitor_output(self):
        """监控进程输出"""
        if not self.process:
            return

        try:
            for line in iter(self.process.stdout.readline, ''):
                if line:
                    line = line.strip()
                    self.output_queue.put(line)

                    # 检测启动完成
                    if "http://0.0.0.0:8188" in line:
                        self.startup_complete = True
                        self.output_queue.put("=== 启动完成! ===")

                # 检查进程是否还在运行 - 添加None检查
                if self.process is None or self.process.poll() is not None:
                    break

        except Exception as e:
            self.output_queue.put(f"监控输出时出错: {str(e)}")
        finally:
            self.is_running = False

# 创建进程管理器实例
process_manager = ProcessManager()

@app.get("/", response_class=HTMLResponse)
async def read_root(request: Request):
    return templates.TemplateResponse("index.html", {"request": request})

@app.post("/start")
async def start_process():
    """启动进程"""
    result = process_manager.start_process()
    return result

@app.post("/restart")
async def restart_process():
    """重启进程"""
    result = process_manager.restart_process()
    return result

@app.post("/stop")
async def stop_process():
    """停止进程"""
    # 获取停止前的进程信息
    before_info = process_manager.get_process_info()

    process_manager.stop_process()

    # 等待一下让进程完全停止
    await asyncio.sleep(1)

    # 获取停止后的进程信息
    after_info = process_manager.get_process_info()

    return {
        "status": "success",
        "message": "进程停止命令已执行",
        "before_stop": before_info,
        "after_stop": after_info
    }

@app.get("/status")
async def get_status():
    """获取进程状态"""
    return {
        "is_running": process_manager.is_running,
        "startup_complete": process_manager.startup_complete,
        "process_alive": process_manager.process is not None and process_manager.process.poll() is None,
        "process_info": process_manager.get_process_info()
    }

@app.get("/check-system-processes")
async def check_system_processes():
    """检查系统中是否还有相关进程在运行"""
    try:
        running_processes = []

        # 检查所有进程
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''

                # 检查是否是我们要找的进程
                if any(keyword in cmdline for keyword in [
                    'hai run python3.12 main.py',
                    'python3.12 main.py --listen',
                    'ComfyUI',
                    'main.py --listen'
                ]):
                    running_processes.append({
                        'pid': proc.info['pid'],
                        'name': proc.info['name'],
                        'cmdline': cmdline
                    })

            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                pass

        return {
            "status": "success",
            "running_processes": running_processes,
            "count": len(running_processes)
        }

    except Exception as e:
        return {
            "status": "error",
            "message": f"检查系统进程时出错: {str(e)}"
        }

@app.post("/force-kill-all")
async def force_kill_all():
    """强制杀死所有相关进程"""
    try:
        killed_processes = []

        # 首先停止我们管理的进程
        process_manager.stop_process()

        # 然后查找并杀死所有相关进程
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''

                # 检查是否是我们要找的进程
                if any(keyword in cmdline for keyword in [
                    'hai run python3.12 main.py',
                    'python3.12 main.py --listen',
                    'ComfyUI',
                    'main.py --listen'
                ]):
                    try:
                        proc.kill()
                        killed_processes.append({
                            'pid': proc.info['pid'],
                            'name': proc.info['name'],
                            'cmdline': cmdline
                        })
                        print(f"强制杀死进程 PID: {proc.info['pid']}")
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        pass

            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                pass

        # 额外的系统级清理
        try:
            os.system("pkill -f 'hai run python3.12 main.py'")
            os.system("pkill -f 'python3.12 main.py --listen'")
            os.system("pkill -f 'ComfyUI'")
        except Exception as e:
            print(f"系统级清理出错: {e}")

        return {
            "status": "success",
            "message": f"强制清理完成，杀死了 {len(killed_processes)} 个进程",
            "killed_processes": killed_processes
        }

    except Exception as e:
        return {
            "status": "error",
            "message": f"强制清理时出错: {str(e)}"
        }

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket端点，用于实时输出日志"""
    await websocket.accept()

    try:
        while True:
            # 检查是否有新的输出
            try:
                while not process_manager.output_queue.empty():
                    line = process_manager.output_queue.get_nowait()
                    await websocket.send_text(json.dumps({
                        "type": "output",
                        "data": line
                    }))
            except queue.Empty:
                pass

            # 发送状态更新
            status = {
                "type": "status",
                "data": {
                    "is_running": process_manager.is_running,
                    "startup_complete": process_manager.startup_complete,
                    "process_alive": process_manager.process is not None and process_manager.process.poll() is None
                }
            }
            await websocket.send_text(json.dumps(status))

            await asyncio.sleep(0.5)  # 每500ms检查一次

    except Exception as e:
        print(f"WebSocket错误: {e}")
    finally:
        await websocket.close()

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
