from fastapi import FastAP<PERSON>, WebSocket, Request
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
import subprocess
import asyncio
import threading
import queue
import json
from typing import Optional
import os
import signal
import psutil
import time

app = FastAPI()

# 挂载静态文件和模板
app.mount("/static", StaticFiles(directory="static"), name="static")
templates = Jinja2Templates(directory="templates")

# 全局变量
process: Optional[subprocess.Popen] = None
output_queue = queue.Queue()
is_running = False
startup_complete = False

class ProcessManager:
    def __init__(self):
        self.process = None
        self.output_queue = queue.Queue()
        self.is_running = False
        self.startup_complete = False

    def start_process(self):
        """启动进程"""
        if self.process is not None and self.process.poll() is None:
            return {"status": "error", "message": "进程已在运行"}

        try:
            # 设置工作目录为ComfyUI路径
            comfyui_path = "/workspace/cpfs-data/ComfyUI"

            self.process = subprocess.Popen(
                ["hai", "run", "python3.12", "main.py", "--listen"],
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1,
                cwd=comfyui_path,  # 设置工作目录
                preexec_fn=os.setsid if os.name != 'nt' else None  # 创建新的进程组
            )
            self.is_running = True
            self.startup_complete = False

            # 启动输出监控线程
            threading.Thread(target=self._monitor_output, daemon=True).start()

            return {"status": "success", "message": "进程启动中..."}
        except Exception as e:
            return {"status": "error", "message": f"启动失败: {str(e)}"}

    def restart_process(self):
        """重启进程"""
        self.stop_process()
        # 等待进程完全停止
        if self.process:
            self.process.wait()
        return self.start_process()

    def stop_process(self):
        """停止进程 - 专门针对hai run和GPU资源清理"""
        if self.process is not None and self.process.poll() is None:
            try:
                pid = self.process.pid
                print(f"正在停止 hai run 进程 PID: {pid}")

                # 方法1: 尝试优雅停止主进程
                self.process.terminate()
                try:
                    self.process.wait(timeout=3)
                    print("hai run 主进程已优雅停止")
                except subprocess.TimeoutExpired:
                    print("优雅停止超时，开始强制清理")

                    # 方法2: 强制杀死主进程
                    try:
                        self.process.kill()
                        self.process.wait(timeout=2)
                    except subprocess.TimeoutExpired:
                        pass

                    # 方法3: 使用psutil杀死进程组
                    try:
                        parent = psutil.Process(pid)
                        children = parent.children(recursive=True)

                        # 杀死所有子进程
                        for child in children:
                            try:
                                print(f"杀死子进程 PID: {child.pid}")
                                child.kill()
                            except (psutil.NoSuchProcess, psutil.AccessDenied):
                                pass

                        # 杀死主进程
                        try:
                            parent.kill()
                            print(f"杀死主进程 PID: {pid}")
                        except (psutil.NoSuchProcess, psutil.AccessDenied):
                            pass

                    except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
                        print(f"使用psutil停止进程时出错: {e}")

            except Exception as e:
                print(f"停止进程时出错: {e}")

        # 方法4: 专门针对hai run的清理
        print("开始清理 hai run 相关进程...")
        try:
            # 停止所有hai相关进程
            os.system("pkill -f 'hai run'")
            os.system("pkill -f 'hai'")
            print("已清理 hai 相关进程")

            # 停止ComfyUI相关进程
            os.system("pkill -f 'python3.12 main.py'")
            os.system("pkill -f 'main.py --listen'")
            os.system("pkill -f 'ComfyUI'")
            print("已清理 ComfyUI 相关进程")

            # 等待进程完全停止
            time.sleep(2)

        except Exception as e:
            print(f"系统级清理出错: {e}")

        # 方法5: GPU资源清理
        print("开始清理 GPU 资源...")
        try:
            # 尝试清理GPU内存 (如果有nvidia-smi)
            os.system("nvidia-smi --gpu-reset-ecc=0,1 2>/dev/null || true")

            # 杀死所有可能占用GPU的Python进程
            os.system("pkill -f 'python.*torch'")
            os.system("pkill -f 'python.*cuda'")

            print("GPU 资源清理完成")

        except Exception as e:
            print(f"GPU清理出错: {e}")

        # 方法6: 容器清理 (如果hai run使用容器)
        print("开始清理容器资源...")
        try:
            # 停止相关容器
            os.system("docker stop $(docker ps -q --filter ancestor=*comfyui*) 2>/dev/null || true")
            os.system("docker stop $(docker ps -q --filter name=*comfyui*) 2>/dev/null || true")

            # 清理悬挂的容器
            os.system("docker container prune -f 2>/dev/null || true")

            print("容器资源清理完成")

        except Exception as e:
            print(f"容器清理出错: {e}")

        self.is_running = False
        self.startup_complete = False
        self.process = None
        print("进程管理器状态已重置，所有资源清理完成")

    def get_process_info(self):
        """获取进程信息用于调试"""
        if self.process is None:
            return "进程对象为空"

        try:
            pid = self.process.pid
            poll_result = self.process.poll()

            # 使用psutil获取更详细的进程信息
            try:
                proc = psutil.Process(pid)
                children = proc.children(recursive=True)
                return {
                    "pid": pid,
                    "poll_result": poll_result,
                    "psutil_status": proc.status() if proc.is_running() else "not_running",
                    "children_count": len(children),
                    "children_pids": [child.pid for child in children]
                }
            except psutil.NoSuchProcess:
                return {
                    "pid": pid,
                    "poll_result": poll_result,
                    "psutil_status": "no_such_process"
                }
        except Exception as e:
            return f"获取进程信息出错: {e}"

    def _monitor_output(self):
        """监控进程输出"""
        if not self.process:
            return

        try:
            for line in iter(self.process.stdout.readline, ''):
                if line:
                    line = line.strip()
                    self.output_queue.put(line)

                    # 检测启动完成
                    if "http://0.0.0.0:8188" in line:
                        self.startup_complete = True
                        self.output_queue.put("=== 启动完成! ===")

                # 检查进程是否还在运行 - 添加None检查
                if self.process is None or self.process.poll() is not None:
                    break

        except Exception as e:
            self.output_queue.put(f"监控输出时出错: {str(e)}")
        finally:
            self.is_running = False

# 创建进程管理器实例
process_manager = ProcessManager()

@app.get("/", response_class=HTMLResponse)
async def read_root(request: Request):
    return templates.TemplateResponse("index.html", {"request": request})

@app.post("/start")
async def start_process():
    """启动进程"""
    result = process_manager.start_process()
    return result

@app.post("/restart")
async def restart_process():
    """重启进程"""
    result = process_manager.restart_process()
    return result

@app.post("/stop")
async def stop_process():
    """停止进程"""
    # 获取停止前的进程信息
    before_info = process_manager.get_process_info()

    process_manager.stop_process()

    # 等待一下让进程完全停止
    await asyncio.sleep(1)

    # 获取停止后的进程信息
    after_info = process_manager.get_process_info()

    return {
        "status": "success",
        "message": "进程停止命令已执行",
        "before_stop": before_info,
        "after_stop": after_info
    }

@app.get("/status")
async def get_status():
    """获取进程状态"""
    return {
        "is_running": process_manager.is_running,
        "startup_complete": process_manager.startup_complete,
        "process_alive": process_manager.process is not None and process_manager.process.poll() is None,
        "process_info": process_manager.get_process_info()
    }

@app.get("/check-system-processes")
async def check_system_processes():
    """检查系统中是否还有相关进程在运行"""
    try:
        running_processes = []

        # 检查所有进程
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''

                # 检查是否是我们要找的进程 - 扩展检测范围
                if any(keyword in cmdline for keyword in [
                    'hai run',
                    'hai',
                    'python3.12 main.py',
                    'main.py --listen',
                    'ComfyUI',
                    'torch',
                    'cuda',
                    '/workspace/cpfs-data/ComfyUI'
                ]) or any(keyword in proc.info['name'].lower() for keyword in [
                    'python3.12',
                    'hai'
                ]):
                    running_processes.append({
                        'pid': proc.info['pid'],
                        'name': proc.info['name'],
                        'cmdline': cmdline
                    })

            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                pass

        return {
            "status": "success",
            "running_processes": running_processes,
            "count": len(running_processes)
        }

    except Exception as e:
        return {
            "status": "error",
            "message": f"检查系统进程时出错: {str(e)}"
        }

@app.post("/force-kill-all")
async def force_kill_all():
    """强制杀死所有相关进程"""
    try:
        killed_processes = []

        # 首先停止我们管理的进程
        process_manager.stop_process()

        # 然后查找并杀死所有相关进程
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''

                # 检查是否是我们要找的进程 - 扩展检测范围
                if any(keyword in cmdline for keyword in [
                    'hai run',
                    'hai',
                    'python3.12 main.py',
                    'main.py --listen',
                    'ComfyUI',
                    'torch',
                    'cuda',
                    '/workspace/cpfs-data/ComfyUI'
                ]) or any(keyword in proc.info['name'].lower() for keyword in [
                    'python3.12',
                    'hai'
                ]):
                    try:
                        proc.kill()
                        killed_processes.append({
                            'pid': proc.info['pid'],
                            'name': proc.info['name'],
                            'cmdline': cmdline
                        })
                        print(f"强制杀死进程 PID: {proc.info['pid']}")
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        pass

            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                pass

        # 额外的系统级清理
        try:
            os.system("pkill -f 'hai run python3.12 main.py'")
            os.system("pkill -f 'python3.12 main.py --listen'")
            os.system("pkill -f 'ComfyUI'")
        except Exception as e:
            print(f"系统级清理出错: {e}")

        return {
            "status": "success",
            "message": f"强制清理完成，杀死了 {len(killed_processes)} 个进程",
            "killed_processes": killed_processes
        }

    except Exception as e:
        return {
            "status": "error",
            "message": f"强制清理时出错: {str(e)}"
        }

@app.get("/check-gpu-usage")
async def check_gpu_usage():
    """检查GPU使用情况"""
    try:
        gpu_info = {}

        # 尝试使用nvidia-smi获取GPU信息
        try:
            result = subprocess.run(
                ["nvidia-smi", "--query-gpu=index,name,memory.used,memory.total,utilization.gpu",
                 "--format=csv,noheader,nounits"],
                capture_output=True, text=True, timeout=10
            )

            if result.returncode == 0:
                gpu_lines = result.stdout.strip().split('\n')
                gpu_info['nvidia_smi'] = []

                for line in gpu_lines:
                    if line.strip():
                        parts = [p.strip() for p in line.split(',')]
                        if len(parts) >= 5:
                            gpu_info['nvidia_smi'].append({
                                'index': parts[0],
                                'name': parts[1],
                                'memory_used': parts[2],
                                'memory_total': parts[3],
                                'utilization': parts[4]
                            })
            else:
                gpu_info['nvidia_smi'] = "nvidia-smi 命令执行失败"

        except (subprocess.TimeoutExpired, FileNotFoundError) as e:
            gpu_info['nvidia_smi'] = f"nvidia-smi 不可用: {str(e)}"

        # 检查GPU相关进程
        try:
            result = subprocess.run(
                ["nvidia-smi", "pmon", "-c", "1"],
                capture_output=True, text=True, timeout=10
            )

            if result.returncode == 0:
                gpu_info['gpu_processes'] = result.stdout
            else:
                gpu_info['gpu_processes'] = "无法获取GPU进程信息"

        except (subprocess.TimeoutExpired, FileNotFoundError) as e:
            gpu_info['gpu_processes'] = f"GPU进程监控不可用: {str(e)}"

        # 检查系统中使用GPU的进程
        gpu_using_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''

                # 检查是否可能使用GPU
                if any(keyword in cmdline.lower() for keyword in [
                    'cuda', 'torch', 'tensorflow', 'gpu', 'nvidia',
                    'comfyui', 'python3.12 main.py'
                ]):
                    gpu_using_processes.append({
                        'pid': proc.info['pid'],
                        'name': proc.info['name'],
                        'cmdline': cmdline
                    })

            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                pass

        gpu_info['gpu_using_processes'] = gpu_using_processes

        return {
            "status": "success",
            "gpu_info": gpu_info
        }

    except Exception as e:
        return {
            "status": "error",
            "message": f"检查GPU使用情况时出错: {str(e)}"
        }

@app.get("/system-resources")
async def get_system_resources():
    """获取系统资源信息"""
    try:
        # 获取内存信息
        memory = psutil.virtual_memory()

        # 获取CPU信息
        cpu_percent = psutil.cpu_percent(interval=1)

        # 获取磁盘信息
        disk = psutil.disk_usage('/')

        return {
            "status": "success",
            "memory": {
                "total": memory.total,
                "used": memory.used,
                "available": memory.available,
                "percent": round(memory.percent, 1)
            },
            "cpu": {
                "percent": round(cpu_percent, 1)
            },
            "disk": {
                "total": disk.total,
                "used": disk.used,
                "free": disk.free,
                "percent": round((disk.used / disk.total) * 100, 1)
            }
        }

    except Exception as e:
        return {
            "status": "error",
            "message": f"获取系统资源信息时出错: {str(e)}"
        }

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket端点，用于实时输出日志"""
    await websocket.accept()

    try:
        while True:
            # 检查是否有新的输出
            try:
                while not process_manager.output_queue.empty():
                    line = process_manager.output_queue.get_nowait()
                    await websocket.send_text(json.dumps({
                        "type": "output",
                        "data": line
                    }))
            except queue.Empty:
                pass

            # 发送状态更新
            status = {
                "type": "status",
                "data": {
                    "is_running": process_manager.is_running,
                    "startup_complete": process_manager.startup_complete,
                    "process_alive": process_manager.process is not None and process_manager.process.poll() is None
                }
            }
            await websocket.send_text(json.dumps(status))

            await asyncio.sleep(0.5)  # 每500ms检查一次

    except Exception as e:
        print(f"WebSocket错误: {e}")
    finally:
        await websocket.close()

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
