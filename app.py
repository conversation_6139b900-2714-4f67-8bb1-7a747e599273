from fastapi import FastAP<PERSON>, WebSocket, Request
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
import subprocess
import asyncio
import threading
import queue
import json
from typing import Optional
import os
import signal

app = FastAPI()

# 挂载静态文件和模板
app.mount("/static", StaticFiles(directory="static"), name="static")
templates = Jinja2Templates(directory="templates")

# 全局变量
process: Optional[subprocess.Popen] = None
output_queue = queue.Queue()
is_running = False
startup_complete = False

class ProcessManager:
    def __init__(self):
        self.process = None
        self.output_queue = queue.Queue()
        self.is_running = False
        self.startup_complete = False

    def start_process(self):
        """启动进程"""
        if self.process and self.process.poll() is None:
            return {"status": "error", "message": "进程已在运行"}

        try:
            # 设置工作目录为ComfyUI路径
            comfyui_path = "/workspace/cpfs-data/ComfyUI"

            self.process = subprocess.Popen(
                ["hai", "run", "python3.12", "main.py", "--listen"],
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1,
                cwd=comfyui_path  # 设置工作目录
            )
            self.is_running = True
            self.startup_complete = False

            # 启动输出监控线程
            threading.Thread(target=self._monitor_output, daemon=True).start()

            return {"status": "success", "message": "进程启动中..."}
        except Exception as e:
            return {"status": "error", "message": f"启动失败: {str(e)}"}

    def restart_process(self):
        """重启进程"""
        self.stop_process()
        # 等待进程完全停止
        if self.process:
            self.process.wait()
        return self.start_process()

    def stop_process(self):
        """停止进程"""
        if self.process and self.process.poll() is None:
            try:
                self.process.terminate()
                # 等待进程优雅退出
                try:
                    self.process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    self.process.kill()
                    self.process.wait()
            except Exception as e:
                print(f"停止进程时出错: {e}")

        self.is_running = False
        self.startup_complete = False
        self.process = None

    def _monitor_output(self):
        """监控进程输出"""
        if not self.process:
            return

        try:
            for line in iter(self.process.stdout.readline, ''):
                if line:
                    line = line.strip()
                    self.output_queue.put(line)

                    # 检测启动完成
                    if "http://0.0.0.0:8188" in line:
                        self.startup_complete = True
                        self.output_queue.put("=== 启动完成! ===")

                # 检查进程是否还在运行
                if self.process.poll() is not None:
                    break

        except Exception as e:
            self.output_queue.put(f"监控输出时出错: {str(e)}")
        finally:
            self.is_running = False

# 创建进程管理器实例
process_manager = ProcessManager()

@app.get("/", response_class=HTMLResponse)
async def read_root(request: Request):
    return templates.TemplateResponse("index.html", {"request": request})

@app.post("/start")
async def start_process():
    """启动进程"""
    result = process_manager.start_process()
    return result

@app.post("/restart")
async def restart_process():
    """重启进程"""
    result = process_manager.restart_process()
    return result

@app.post("/stop")
async def stop_process():
    """停止进程"""
    process_manager.stop_process()
    return {"status": "success", "message": "进程已停止"}

@app.get("/status")
async def get_status():
    """获取进程状态"""
    return {
        "is_running": process_manager.is_running,
        "startup_complete": process_manager.startup_complete,
        "process_alive": process_manager.process and process_manager.process.poll() is None
    }

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket端点，用于实时输出日志"""
    await websocket.accept()

    try:
        while True:
            # 检查是否有新的输出
            try:
                while not process_manager.output_queue.empty():
                    line = process_manager.output_queue.get_nowait()
                    await websocket.send_text(json.dumps({
                        "type": "output",
                        "data": line
                    }))
            except queue.Empty:
                pass

            # 发送状态更新
            status = {
                "type": "status",
                "data": {
                    "is_running": process_manager.is_running,
                    "startup_complete": process_manager.startup_complete,
                    "process_alive": process_manager.process and process_manager.process.poll() is None
                }
            }
            await websocket.send_text(json.dumps(status))

            await asyncio.sleep(0.5)  # 每500ms检查一次

    except Exception as e:
        print(f"WebSocket错误: {e}")
    finally:
        await websocket.close()

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
